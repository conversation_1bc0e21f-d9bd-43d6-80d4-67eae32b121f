{% extends "base.html" %}

{% block title %}Home{% endblock %}

{% block content %}
<!-- Hero Section with Enhanced Design -->
<div class="hero-section position-relative overflow-hidden">
    <div class="hero-background"></div>
    <div class="hero-content text-center text-white py-5">
        <div class="container">
            <h1 class="display-3 fw-bold mb-4 animate__animated animate__fadeInDown">
                <i class="bi bi-mortarboard me-3"></i>ApexPlanet
            </h1>
            <p class="lead mb-4 animate__animated animate__fadeInUp animate__delay-1s">
                Master Programming Skills with Our Premium Courses
            </p>
            <p class="h5 mb-5 animate__animated animate__fadeInUp animate__delay-2s">
                Learn from Industry Experts • Build Real Projects • Get Certified
            </p>
            <div class="hero-buttons animate__animated animate__fadeInUp animate__delay-3s">
                <a href="{{ url_for('main.products') }}" class="btn btn-light btn-lg me-3 hero-btn">
                    <i class="bi bi-play-circle me-2"></i>Start Learning
                </a>
                <a href="{{ url_for('main.cart') }}" class="btn btn-outline-light btn-lg hero-btn">
                    <i class="bi bi-cart3 me-2"></i>View Cart
                </a>
            </div>
        </div>
    </div>
    <div class="hero-scroll-indicator">
        <i class="bi bi-chevron-down"></i>
    </div>
</div>

<!-- Featured Courses Section -->
<section class="featured-products py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold mb-3 animate__animated animate__fadeInUp">
                <i class="bi bi-star-fill text-warning me-2"></i>Featured Courses
            </h2>
            <p class="lead text-muted animate__animated animate__fadeInUp animate__delay-1s">
                Start your programming journey with our most popular courses
            </p>
        </div>
        <div class="row">
            {% for product in products %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 featured-card animate__animated animate__fadeInUp" style="animation-delay: {{ loop.index0 * 0.2 }}s;">
                    <div class="course-img-wrapper">
                        <img src="{{ url_for('static', filename=product.image_url) }}"
                             class="card-img-top"
                             alt="{{ product.name }}"
                             onerror="this.onerror=null; this.parentElement.innerHTML='<div class=\'course-placeholder\' data-course=\'{{ product.name }}\'><i class=\'bi bi-code-square\'></i></div>';">
                        <div class="course-overlay">
                            <div class="course-rating">
                                <i class="bi bi-star-fill"></i>
                                {{ product.rating }}
                            </div>
                        </div>
                    </div>
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ product.name }}</h5>
                        <p class="card-text flex-grow-1">{{ product.description[:100] }}...</p>
                        <div class="course-meta mb-3">
                            <span class="badge bg-primary me-2">
                                <i class="bi bi-clock me-1"></i>{{ product.duration }}
                            </span>
                            <span class="badge bg-success">
                                <i class="bi bi-people me-1"></i>Beginner Friendly
                            </span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="h4 text-primary mb-0">${{ "%.2f"|format(product.price) }}</span>
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary add-to-cart" data-product-id="{{ product.id }}">
                                    <i class="bi bi-cart-plus"></i>
                                </button>
                                <a href="{{ url_for('main.product_detail', product_id=product.id) }}"
                                   class="btn btn-outline-primary">
                                    <i class="bi bi-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<section class="categories py-5">
    <h2 class="text-center mb-4">Shop by Category</h2>
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="category-card text-center p-4 bg-light">
                <i class="bi bi-moon-stars display-4"></i>
                <h3>Space Art</h3>
                <p>Beautiful prints and posters of celestial bodies</p>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="category-card text-center p-4 bg-light">
                <i class="bi bi-rocket display-4"></i>
                <h3>Model Ships</h3>
                <p>Detailed replicas of famous spacecraft</p>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="category-card text-center p-4 bg-light">
                <i class="bi bi-star display-4"></i>
                <h3>Collectibles</h3>
                <p>Unique space-themed collectibles</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}
