/* Course images styling */
.course-img-wrapper {
    position: relative;
    height: 200px;
    overflow: hidden;
    background: linear-gradient(135deg, #2c3e50, #3498db);
}

.course-img-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-img-wrapper:hover img {
    transform: scale(1.05);
}

/* Course-specific background gradients */
[data-course*="HTML"] .course-placeholder { 
    background: linear-gradient(135deg, #e44d26, #f16529); 
}
[data-course*="CSS"] .course-placeholder { 
    background: linear-gradient(135deg, #264de4, #2965f1); 
}
[data-course*="JavaScript"] .course-placeholder { 
    background: linear-gradient(135deg, #f0db4f, #323330); 
}
[data-course*="Python"] .course-placeholder { 
    background: linear-gradient(135deg, #306998, #FFD43B); 
}
[data-course*="Java"] .course-placeholder { 
    background: linear-gradient(135deg, #5382a1, #f89820); 
}
[data-course*="Full Stack"] .course-placeholder { 
    background: linear-gradient(135deg, #61DAFB, #764ABC); 
}

.course-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    background-size: cover;
    background-position: center;
    position: relative;
    text-align: center;
    padding: 1rem;
}

.course-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
}

.course-placeholder i {
    position: relative;
    z-index: 1;
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

.course-placeholder span {
    position: relative;
    z-index: 1;
    display: block;
    font-weight: bold;
    margin-top: 0.5rem;
}

.course-img-fallback {
    object-fit: cover;
    height: 200px;
    width: 100%;
    background-size: cover;
    background-position: center;
}
