{% extends "base.html" %}

{% block title %}Cart & Checkout{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="alert alert-warning" id="cart-debug" style="word-break:break-all"></div>
    <div class="row">
        <!-- Shopping Cart -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h3 class="mb-4">Shopping Cart</h3>
                    <div id="cart-items">
                        <!-- Cart items will be dynamically loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Summary -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h3 class="mb-4">Payment Summary</h3>
                    <div class="d-flex justify-content-between mb-3">
                        <span>Subtotal:</span>
                        <span id="subtotal">$0.00</span>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>Tax (10%):</span>
                        <span id="tax">$0.00</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-4">
                        <strong>Total:</strong>
                        <strong id="total">$0.00</strong>
                    </div>

                    <!-- Payment Method Selection -->
                    <div class="mb-4">
                        <h5>Payment Method</h5>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="paymentMethod" id="credit-card" checked>
                            <label class="form-check-label" for="credit-card">
                                Credit Card
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="paymentMethod" id="paypal">
                            <label class="form-check-label" for="paypal">
                                PayPal
                            </label>
                        </div>
                    </div>

                    <!-- Credit Card Information -->
                    <div id="credit-card-form">
                        <div class="mb-3">
                            <label for="card-number" class="form-label">Card Number</label>
                            <input type="text" class="form-control" id="card-number" placeholder="1234 5678 9012 3456">
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="expiry" class="form-label">Expiry Date</label>
                                <input type="text" class="form-control" id="expiry" placeholder="MM/YY">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="cvv" class="form-label">CVV</label>
                                <input type="text" class="form-control" id="cvv" placeholder="123">
                            </div>
                        </div>
                    </div>

                    <button class="btn btn-primary w-100" onclick="processPayment()">
                        Proceed to Payment
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    // Load cart items when page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Show raw cart data for debugging
        document.getElementById('cart-debug').textContent = 'localStorage[cart]: ' + localStorage.getItem('cart');
        try {
            loadCartItems();
        } catch (e) {
            document.getElementById('cart-items').innerHTML = '<div class="alert alert-danger">JS Error: ' + e + '</div>';
        }
    });

    function loadCartItems() {
        const cartItems = JSON.parse(localStorage.getItem('cart')) || [];
        const cartContainer = document.getElementById('cart-items');
        let subtotal = 0;

        // Debug: log cart items
        console.log('Cart items:', cartItems);

        if (cartItems.length === 0) {
            cartContainer.innerHTML = '<p class="text-center">Your cart is empty (localStorage: ' + JSON.stringify(localStorage.getItem('cart')) + ')</p>';
            return;
        }

        const itemsHtml = cartItems.map(item => {
            subtotal += item.price * item.quantity;
            return `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <img src="${item.image_url}" class="img-fluid rounded" alt="${item.name}">
                            </div>
                            <div class="col-md-6">
                                <h5 class="card-title">${item.name}</h5>
                                <p class="card-text text-muted">${item.duration}</p>
                            </div>
                            <div class="col-md-2">
                                <div class="input-group">
                                    <input type="number" class="form-control" value="${item.quantity}" 
                                           min="1" onchange="updateQuantity(${item.id}, this.value)">
                                </div>
                            </div>
                            <div class="col-md-2 text-end">
                                <p class="h5">$${(item.price * item.quantity).toFixed(2)}</p>
                                <button class="btn btn-sm btn-danger" onclick="removeItem(${item.id})">Remove</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        cartContainer.innerHTML = itemsHtml;
        updateTotals(subtotal);
    }

    function updateTotals(subtotal) {
        const tax = subtotal * 0.1;
        const total = subtotal + tax;

        document.getElementById('subtotal').textContent = `$${subtotal.toFixed(2)}`;
        document.getElementById('tax').textContent = `$${tax.toFixed(2)}`;
        document.getElementById('total').textContent = `$${total.toFixed(2)}`;
    }

    function updateQuantity(productId, quantity) {
        const cartItems = JSON.parse(localStorage.getItem('cart')) || [];
        const itemIndex = cartItems.findIndex(item => item.id === productId);
        
        if (itemIndex !== -1) {
            cartItems[itemIndex].quantity = parseInt(quantity);
            localStorage.setItem('cart', JSON.stringify(cartItems));
            loadCartItems();
        }
    }

    function removeItem(productId) {
        const cartItems = JSON.parse(localStorage.getItem('cart')) || [];
        const updatedCart = cartItems.filter(item => item.id !== productId);
        localStorage.setItem('cart', JSON.stringify(updatedCart));
        loadCartItems();
    }

    function processPayment() {
        // Here you would normally integrate with a payment gateway
        alert('Payment processing would happen here. This is just a demo.');
    }
</script>
{% endblock %}
