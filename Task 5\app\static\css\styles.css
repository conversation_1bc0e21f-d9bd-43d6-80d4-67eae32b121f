/* Base styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --background-color: #f8f9fa;
    --text-color: #2c3e50;
}

/* Global optimizations */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Performance optimizations */
img {
    max-width: 100%;
    height: auto;
    /* Enable lazy loading for images */
    loading: lazy;
}

/* Responsive typography */
html {
    font-size: 16px;
}

@media (max-width: 768px) {
    html {
        font-size: 14px;
    }
}

/* Enhanced Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-btn {
    transition: all 0.3s ease;
    border-radius: 50px;
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.hero-scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 2rem;
    animation: bounce 2s infinite;
    cursor: pointer;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

.category-card {
    border-radius: 8px;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.category-card:hover {
    transform: translateY(-5px);
}

.product-card {
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: scale(1.02);
}

/* Product card styles with enhanced animations */
.card {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

.card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
    z-index: 1;
}

.card:hover::before {
    left: 100%;
}

.card-img-top {
    height: 200px;
    object-fit: cover;
    border-top-left-radius: calc(0.375rem - 1px);
    border-top-right-radius: calc(0.375rem - 1px);
}

.card-title {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.card-text {
    color: #6c757d;
    margin-bottom: 1rem;
}

.card .list-unstyled li {
    margin-bottom: 0.5rem;
    color: #495057;
}

.card .list-unstyled i {
    margin-right: 0.5rem;
    color: var(--secondary-color);
}

.card .btn-primary {
    width: 100%;
    padding: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: linear-gradient(45deg, var(--secondary-color), #5dade2);
    border: none;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    z-index: 2;
}

.card .btn-primary:hover {
    background: linear-gradient(45deg, #5dade2, var(--secondary-color));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.card .btn-primary:active {
    transform: translateY(0);
}

/* Add to cart button animation */
.add-to-cart {
    position: relative;
    overflow: hidden;
}

.add-to-cart::after {
    content: '✓ Added!';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    background: #27ae60;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.3s ease;
    opacity: 0;
}

.add-to-cart.added::after {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
}

/* Product image enhancements */
.card-img-top {
    height: 220px;
    object-fit: cover;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    transition: transform 0.4s ease;
}

.card:hover .card-img-top {
    transform: scale(1.1);
}

/* Price styling */
.card .h4 {
    color: var(--accent-color);
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Featured Cards */
.featured-card {
    border: none;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: white;
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.featured-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0,0,0,0.2);
}

.course-img-wrapper {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.course-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.3));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.featured-card:hover .course-overlay {
    opacity: 1;
}

.course-rating {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255,255,255,0.9);
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: bold;
    color: #f39c12;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.course-meta .badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 15px;
}

.course-placeholder {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
}

/* Button Groups */
.btn-group .btn {
    border-radius: 10px;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.btn-group .btn:hover {
    transform: scale(1.1);
}

/* Responsive optimizations */
@media (max-width: 576px) {
    .container {
        padding: 0 1rem;
    }
    
    .hero {
        padding: 2rem 0;
    }
}

/* Animation optimizations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Cart badge */
.cart-badge {
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
}

.cart-badge:hover {
    transform: scale(1.1);
}

.cart-badge[data-count]:after {
    content: attr(data-count);
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(45deg, var(--accent-color), #ff6b6b);
    color: white;
    border-radius: 50%;
    padding: 0.2rem 0.5rem;
    font-size: 0.7rem;
    font-weight: bold;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.cart-badge[data-count="0"]:after {
    display: none;
}

/* Pulse animation for cart badge */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Loading spinner */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--secondary-color);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

/* Notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    z-index: 1000;
    animation: slideInRight 0.5s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced Cart Styles */
.cart-section {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.cart-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 1rem;
}

.cart-items-container {
    max-height: 600px;
    overflow-y: auto;
}

.cart-item {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.cart-item:hover {
    background: white;
    border-color: var(--secondary-color);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.item-content {
    display: grid;
    grid-template-columns: 100px 1fr auto auto;
    gap: 1.5rem;
    align-items: center;
}

.item-image img {
    width: 100px;
    height: 80px;
    object-fit: cover;
    border-radius: 10px;
}

.item-details {
    min-width: 0;
}

.item-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.item-meta {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.item-description {
    color: #6c757d;
    font-size: 0.85rem;
    margin: 0;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-input {
    width: 60px;
    text-align: center;
}

.item-price {
    text-align: right;
}

.current-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.remove-btn {
    margin-top: 0.5rem;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
    transform: scale(1.05);
}

/* Payment Summary Styles */
.payment-summary {
    position: sticky;
    top: 2rem;
}

.summary-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.summary-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.total-row {
    font-size: 1.1rem;
    padding: 1rem 0;
    border-top: 2px solid #f8f9fa;
    margin-top: 1rem;
}

.checkout-btn {
    background: linear-gradient(45deg, var(--secondary-color), #5dade2);
    border: none;
    border-radius: 15px;
    padding: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.checkout-btn:hover {
    background: linear-gradient(45deg, #5dade2, var(--secondary-color));
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
}

/* Empty Cart Styles */
.empty-cart {
    background: #f8f9fa;
    border-radius: 20px;
    padding: 3rem;
}

/* Responsive Cart Design */
@media (max-width: 768px) {
    .item-content {
        grid-template-columns: 80px 1fr;
        gap: 1rem;
    }

    .item-quantity,
    .item-price {
        grid-column: 1 / -1;
        margin-top: 1rem;
        text-align: left;
    }

    .quantity-controls {
        justify-content: center;
    }
}

/* Performance optimization for animations */
@media (prefers-reduced-motion: reduce) {
    * {
        animation: none !important;
        transition: none !important;
    }
}

/* Product grid responsiveness */
@media (max-width: 768px) {
    .card-img-top {
        height: 180px;
    }
}

@media (max-width: 576px) {
    .card-img-top {
        height: 160px;
    }
}
