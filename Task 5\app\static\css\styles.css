/* Base styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --background-color: #f8f9fa;
    --text-color: #2c3e50;
}

/* Global optimizations */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Performance optimizations */
img {
    max-width: 100%;
    height: auto;
    /* Enable lazy loading for images */
    loading: lazy;
}

/* Responsive typography */
html {
    font-size: 16px;
}

@media (max-width: 768px) {
    html {
        font-size: 14px;
    }
}

/* Custom components */
.hero {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 4rem 0;
    margin-bottom: 2rem;
}

.category-card {
    border-radius: 8px;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.category-card:hover {
    transform: translateY(-5px);
}

.product-card {
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: scale(1.02);
}

/* Product card styles */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
    border-top-left-radius: calc(0.375rem - 1px);
    border-top-right-radius: calc(0.375rem - 1px);
}

.card-title {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.card-text {
    color: #6c757d;
    margin-bottom: 1rem;
}

.card .list-unstyled li {
    margin-bottom: 0.5rem;
    color: #495057;
}

.card .list-unstyled i {
    margin-right: 0.5rem;
    color: var(--secondary-color);
}

.card .btn-primary {
    width: 100%;
    padding: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive optimizations */
@media (max-width: 576px) {
    .container {
        padding: 0 1rem;
    }
    
    .hero {
        padding: 2rem 0;
    }
}

/* Animation optimizations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Cart badge */
.cart-badge {
    position: relative;
    display: inline-block;
}

.cart-badge[data-count]:after {
    content: attr(data-count);
    position: absolute;
    top: -10px;
    right: -10px;
    background: var(--accent-color);
    color: white;
    border-radius: 50%;
    padding: 0.2rem 0.5rem;
    font-size: 0.75rem;
}

/* Performance optimization for animations */
@media (prefers-reduced-motion: reduce) {
    * {
        animation: none !important;
        transition: none !important;
    }
}

/* Product grid responsiveness */
@media (max-width: 768px) {
    .card-img-top {
        height: 180px;
    }
}

@media (max-width: 576px) {
    .card-img-top {
        height: 160px;
    }
}
